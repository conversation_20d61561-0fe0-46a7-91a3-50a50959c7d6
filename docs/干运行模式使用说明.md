# 干运行模式使用说明

## 功能概述

干运行模式（Dry Run Mode）是一个新增的功能，允许您在生产环境下运行程序，但只打印下单信息而不执行实际的下单操作。这对于验证策略逻辑、检查下单信息的准确性非常有用。

## 配置方法

在 `config.py` 文件中设置：

```python
is_debug = False      # 关闭调试模式，启用生产模式
is_dry_run = True     # 启用干运行模式
```

## 运行模式对比

| 模式 | is_debug | is_dry_run | 行为描述 |
|------|----------|------------|----------|
| 调试模式 | True | 任意 | 模拟运行，使用本地数据，不执行下单 |
| 干运行模式 | False | True | 生产环境运行，连接交易所，只打印下单信息不执行下单 |
| 生产模式 | False | False | 生产环境运行，连接交易所，执行实际下单 |

## 使用场景

### 1. 策略验证
- 验证选币逻辑是否正确
- 检查目标仓位计算是否准确
- 确认清仓、建仓、调仓逻辑

### 2. 配置测试
- 测试新的策略配置
- 验证账户配置是否正确
- 检查黑白名单设置

### 3. 风险控制
- 在重要市场事件前验证策略行为
- 测试新版本代码的稳定性
- 确认seed_coins保护机制

## 输出信息

干运行模式会输出以下信息：

1. **目标仓位信息**：显示所有非零目标持仓的币种
2. **下单信息详情**：包含建仓、调仓、清仓的具体信息
3. **交易模式标识**：清楚标明每个币种的交易类型
4. **运行模式提示**：明确显示当前处于干运行模式

## 示例输出

```
🚀 [BEL黄_过滤版] 开始
🚫 干运行模式已启用 - 只打印下单信息，不执行实际下单

目标仓位：
        symbol  当前持仓量  目标持仓量  目标下单份数   实际下单量 交易模式
AUDIOUSDT         0.0 -2891.524948          -3.0 -2891.524948     建仓
BANDUSDT        241.1     0.000000           NaN  -241.100000     清仓
C98USDT        -583.0     0.000000           NaN   583.000000     清仓

下单信息：
        symbol  实际下单量  实际下单资金 交易模式 symbol_type
AUDIOUSDT -2891.524948    -5000.0     建仓        swap
BANDUSDT   -241.100000     -500.0     清仓        swap

🚫 干运行模式已启用 - 只打印下单信息，不执行实际下单操作
如需执行实际下单，请在config.py中设置 is_dry_run = False
```

## 注意事项

1. **数据来源**：干运行模式使用真实的交易所数据和账户信息
2. **网络连接**：需要正常的网络连接到交易所API
3. **账户状态**：会读取真实的账户持仓和余额信息
4. **不执行下单**：绝对不会执行任何实际的买卖操作
5. **seed_coins保护**：UNIUSDT和LTCUSDT等seed_coins仍然受到完全保护

## 从干运行切换到生产模式

当您确认下单信息正确后，可以切换到生产模式：

```python
is_debug = False      # 保持生产模式
is_dry_run = False    # 关闭干运行模式，启用实际下单
```

## 安全提醒

- 干运行模式是额外的安全保障，但不能替代充分的策略测试
- 建议先在调试模式下充分测试，再使用干运行模式验证
- 切换到生产模式前，请仔细检查所有配置参数
